# 🎫 Ticket System Setup Guide

## 🚨 **IMPORTANT: Database Update Required**

The ticket system requires new database tables that haven't been created yet. You have **two options** to fix this:

### Option 1: Quick Fix (Recommended)
Run this command in your Discord server:
```
.ticketupdate
```
This will create the necessary database tables without restarting the bot.

### Option 2: Bot Restart
Restart your bot completely. The database tables will be created automatically during startup.

---

## 🎯 **After Database Update**

Once you've updated the database, you can use the ticket system:

### 1. **Configure the System**
```
.ticketsetup
```
This opens an interactive configuration panel with buttons:

- **🔧 Set Role** - Choose which role can manage tickets
- **📁 Set Category** - Set category for ticket threads (optional)
- **🎨 Panel Settings** - Customize title, description, color, emoji
- **🔘 Button Style** - Choose button appearance (Primary/Secondary/Success/Danger)
- **📋 Dropdown Toggle** - Switch between button and dropdown interfaces
- **📤 Send Panel** - Deploy the ticket panel immediately
- **🔄 Refresh** - Update the configuration display

### 2. **Deploy Ticket Panel**
After configuration, either:
- Use the "Send Panel" button in the setup interface, OR
- Run: `.ticketpanel`

### 3. **Choose Interface Style**

#### Button Interface (Default)
- Simple "Create Ticket" button
- One-click ticket creation
- Clean, minimal appearance

#### Dropdown Interface
- Professional categorized system
- Options: General Support, Bug Report, Feature Request, Other
- Users select ticket type from dropdown menu

---

## 🎨 **Customization Options**

### Panel Appearance
- **Title**: Custom panel title (e.g., "🎫 Support Center")
- **Description**: Custom description text
- **Color**: Hex color codes (e.g., #FF5733)
- **Emoji**: Custom button emoji

### Button Styles
- **Primary**: Blue button (default)
- **Secondary**: Gray button
- **Success**: Green button
- **Danger**: Red button

### Advanced Features
- **Private Threads**: Tickets are created as private threads
- **Auto-Moderator Addition**: Moderators automatically added to tickets
- **Smart Priority**: Online moderators added first for faster response
- **Persistent Storage**: All data survives bot restarts
- **Easy Management**: Use `.close` command in ticket threads

---

## 🚀 **Quick Start Example**

1. **Update Database**:
   ```
   .ticketupdate
   ```

2. **Configure System**:
   ```
   .ticketsetup
   ```
   - Click "Set Role" → Enter your moderator role
   - Click "Panel Settings" → Customize appearance
   - Click "Send Panel" → Deploy immediately

3. **Done!** Users can now create tickets by clicking the button/dropdown

---

## 🔧 **Troubleshooting**

### "Column server_id does not exist" Error
- Run `.ticketupdate` to create the database tables
- If that fails, restart the bot completely

### "Please set a moderator role first"
- Use `.ticketsetup` and click "Set Role"
- Enter your moderator role name or ID

### Tickets not creating properly
- Ensure the bot has permissions to create threads
- Check that the moderator role exists and has proper permissions
- Verify the bot can add users to threads

---

## 📋 **Available Commands**

| Command | Description |
|---------|-------------|
| `.ticketsetup` | Interactive configuration panel |
| `.ticketpanel` | Send ticket panel with current settings |
| `.ticketupdate` | Force update database tables |
| `.close` | Close current ticket thread (in tickets only) |
| `.boosters` | View current server boosters |
| `.boosterslost` | View recently lost boosters |

---

## ✅ **Features Included**

- ✅ **Interactive Setup** - Button-based configuration
- ✅ **Private Threads** - Secure ticket conversations
- ✅ **Smart Moderator Management** - Auto-adds online moderators
- ✅ **Dropdown Categories** - Professional ticket types
- ✅ **Custom Styling** - Full appearance customization
- ✅ **Persistent Data** - Survives bot restarts
- ✅ **Booster Tracking** - Monitor server boost status
- ✅ **Easy Management** - Simple close commands

**The system is now ready for production use!**
