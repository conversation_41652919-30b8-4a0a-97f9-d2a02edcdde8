import discord
from discord.ext import commands
import asyncio
import random

from utilities import checks
from utilities import decorators

class Ticket(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.panel_message_id = None
        self.active_tickets = {}  # Store active tickets {user_id: thread_id}
        self.ticket_configs = {}  # Cache for ticket configurations

    async def load_ticket_config(self, guild_id):
        """Load ticket configuration from database"""
        if guild_id in self.ticket_configs:
            return self.ticket_configs[guild_id]

        query = """
                SELECT * FROM ticket_config
                WHERE server_id = $1;
                """
        config = await self.bot.cxn.fetchrow(query, guild_id)

        if not config:
            # Create default config
            default_config = {
                'server_id': guild_id,
                'ticket_role_id': None,
                'ticket_category_id': None,
                'panel_title': '🎫 Contact a Moderator',
                'panel_description': 'Click the button below to **open a private ticket** with the moderators.',
                'panel_color': '#5865F2'
            }

            query = """
                    INSERT INTO ticket_config (server_id, ticket_role_id, ticket_category_id, panel_title, panel_description, panel_color)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (server_id) DO NOTHING;
                    """
            await self.bot.cxn.execute(query, guild_id, None, None, default_config['panel_title'],
                                     default_config['panel_description'], default_config['panel_color'])

            self.ticket_configs[guild_id] = default_config
            return default_config

        config_dict = dict(config)
        self.ticket_configs[guild_id] = config_dict
        return config_dict

    async def load_active_tickets(self, guild_id):
        """Load active tickets from database"""
        query = """
                SELECT user_id, thread_id FROM active_tickets
                WHERE server_id = $1;
                """
        records = await self.bot.cxn.fetch(query, guild_id)

        for record in records:
            self.active_tickets[record['user_id']] = record['thread_id']

    @decorators.command(
        brief="Configure ticket system settings.",
        aliases=["ticketconfig", "tconfig"],
        examples="""
                {0}ticketsetup role @Moderators
                {0}ticketsetup category Tickets
                {0}ticketsetup title "🎫 Support Tickets"
                {0}ticketsetup description "Click below to create a support ticket"
                {0}ticketsetup color #FF5733
                """,
    )
    @checks.has_perms(administrator=True)
    @checks.bot_has_perms(embed_links=True)
    async def ticketsetup(self, ctx, setting: str = None, *, value: str = None):
        """
        Usage: {0}ticketsetup <setting> <value>
        Aliases: {0}ticketconfig, {0}tconfig
        Output: Configure ticket system settings
        Settings:
            role - Set the moderator role for tickets
            category - Set the category for ticket threads (optional)
            title - Set the panel title
            description - Set the panel description
            color - Set the panel embed color (hex format)
        """
        if not setting:
            # Show current configuration
            config = await self.load_ticket_config(ctx.guild.id)

            embed = discord.Embed(
                title="🎫 Ticket System Configuration",
                color=self.bot.mode.EMBED_COLOR
            )

            role = ctx.guild.get_role(config['ticket_role_id']) if config['ticket_role_id'] else None
            category = ctx.guild.get_channel(config['ticket_category_id']) if config['ticket_category_id'] else None

            embed.add_field(name="Moderator Role", value=role.mention if role else "Not set", inline=True)
            embed.add_field(name="Category", value=category.name if category else "Not set", inline=True)
            embed.add_field(name="Panel Title", value=config['panel_title'], inline=False)
            embed.add_field(name="Panel Description", value=config['panel_description'], inline=False)
            embed.add_field(name="Panel Color", value=config['panel_color'], inline=True)

            return await ctx.send_or_reply(embed=embed)

        if not value:
            return await ctx.fail(f"Please provide a value for the `{setting}` setting.")

        setting = setting.lower()
        config = await self.load_ticket_config(ctx.guild.id)

        if setting == "role":
            try:
                role = await commands.RoleConverter().convert(ctx, value)
                query = """
                        UPDATE ticket_config
                        SET ticket_role_id = $1, updated_at = NOW()
                        WHERE server_id = $2;
                        """
                await self.bot.cxn.execute(query, role.id, ctx.guild.id)
                self.ticket_configs[ctx.guild.id]['ticket_role_id'] = role.id
                await ctx.success(f"Ticket moderator role set to {role.mention}")
            except commands.BadArgument:
                await ctx.fail("Invalid role provided.")

        elif setting == "category":
            try:
                category = await commands.CategoryChannelConverter().convert(ctx, value)
                query = """
                        UPDATE ticket_config
                        SET ticket_category_id = $1, updated_at = NOW()
                        WHERE server_id = $2;
                        """
                await self.bot.cxn.execute(query, category.id, ctx.guild.id)
                self.ticket_configs[ctx.guild.id]['ticket_category_id'] = category.id
                await ctx.success(f"Ticket category set to {category.name}")
            except commands.BadArgument:
                await ctx.fail("Invalid category provided.")

        elif setting == "title":
            if len(value) > 256:
                return await ctx.fail("Title must be 256 characters or less.")

            query = """
                    UPDATE ticket_config
                    SET panel_title = $1, updated_at = NOW()
                    WHERE server_id = $2;
                    """
            await self.bot.cxn.execute(query, value, ctx.guild.id)
            self.ticket_configs[ctx.guild.id]['panel_title'] = value
            await ctx.success(f"Panel title set to: {value}")

        elif setting == "description":
            if len(value) > 4096:
                return await ctx.fail("Description must be 4096 characters or less.")

            query = """
                    UPDATE ticket_config
                    SET panel_description = $1, updated_at = NOW()
                    WHERE server_id = $2;
                    """
            await self.bot.cxn.execute(query, value, ctx.guild.id)
            self.ticket_configs[ctx.guild.id]['panel_description'] = value
            await ctx.success(f"Panel description updated.")

        elif setting == "color":
            # Validate hex color
            if not value.startswith('#'):
                value = '#' + value

            try:
                int(value[1:], 16)  # Test if valid hex
                if len(value) != 7:
                    raise ValueError
            except ValueError:
                return await ctx.fail("Invalid hex color format. Use format: #FF5733")

            query = """
                    UPDATE ticket_config
                    SET panel_color = $1, updated_at = NOW()
                    WHERE server_id = $2;
                    """
            await self.bot.cxn.execute(query, value, ctx.guild.id)
            self.ticket_configs[ctx.guild.id]['panel_color'] = value
            await ctx.success(f"Panel color set to: {value}")

        else:
            await ctx.fail("Invalid setting. Valid options: `role`, `category`, `title`, `description`, `color`")

    @decorators.command(
        brief="Send the ticket panel.",
        aliases=["tpanel"],
        examples="""
                {0}ticketpanel
                """,
    )
    @checks.has_perms(administrator=True)
    @checks.bot_has_perms(embed_links=True)
    async def ticketpanel(self, ctx):
        """
        Usage: {0}ticketpanel
        Alias: {0}tpanel
        Output: Send the ticket panel with current configuration
        """
        config = await self.load_ticket_config(ctx.guild.id)

        if not config['ticket_role_id']:
            return await ctx.fail("Please set a moderator role first using `ticketsetup role @Role`")

        try:
            color = int(config['panel_color'][1:], 16)
        except:
            color = 0x5865F2

        embed = discord.Embed(
            title=config['panel_title'],
            description=config['panel_description'],
            color=color
        )

        view = TicketPanelView(self)
        msg = await ctx.send(embed=embed, view=view)
        self.panel_message_id = msg.id

    @decorators.command(
        brief="Close a ticket thread.",
        aliases=["closeticket"],
        examples="""
                {0}close
                """,
    )
    @checks.has_perms(manage_messages=True)
    async def close(self, ctx):
        """
        Usage: {0}close
        Alias: {0}closeticket
        Output: Close the current ticket thread
        """
        if not isinstance(ctx.channel, discord.Thread):
            return await ctx.send("❌ This command can only be used in ticket threads.")

        if not ctx.channel.name.startswith("ticket-"):
            return await ctx.send("❌ This doesn't appear to be a ticket thread.")

        # Archive and lock the thread
        await ctx.channel.edit(archived=True, locked=True)

        # Update channel name to show it's resolved
        try:
            new_name = f"[Resolved] {ctx.channel.name}"
            await ctx.channel.edit(name=new_name)
        except:
            pass

        # Remove from active tickets database
        query = """
                DELETE FROM active_tickets
                WHERE server_id = $1 AND thread_id = $2;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, ctx.channel.id)

        # Remove from memory cache
        user_id = None
        for uid, thread_id in self.active_tickets.items():
            if thread_id == ctx.channel.id:
                user_id = uid
                break

        if user_id:
            del self.active_tickets[user_id]

        embed = discord.Embed(
            description="🔒 This ticket has been locked and archived. Still need help? Create another ticket in the main channel.",
            color=0x57F287
        )
        await ctx.send(embed=embed)

class TicketPanelView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.button(label="Create Ticket", style=discord.ButtonStyle.blurple, custom_id="create_ticket")
    async def create_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Load ticket configuration
        config = await self.cog.load_ticket_config(interaction.guild.id)

        if not config['ticket_role_id']:
            return await interaction.response.send_message(
                "❌ Ticket system is not properly configured. Please contact an administrator.",
                ephemeral=True
            )

        # Check if user already has an active ticket
        query = """
                SELECT thread_id FROM active_tickets
                WHERE server_id = $1 AND user_id = $2;
                """
        existing_ticket = await self.cog.bot.cxn.fetchrow(query, interaction.guild.id, interaction.user.id)

        if existing_ticket:
            try:
                existing_thread = interaction.guild.get_thread(existing_ticket['thread_id'])
                if existing_thread and not existing_thread.archived:
                    return await interaction.response.send_message(
                        f"❌ You already have an active ticket: {existing_thread.mention}",
                        ephemeral=True
                    )
                else:
                    # Clean up old reference from database
                    query = """
                            DELETE FROM active_tickets
                            WHERE server_id = $1 AND user_id = $2;
                            """
                    await self.cog.bot.cxn.execute(query, interaction.guild.id, interaction.user.id)
            except:
                # Thread doesn't exist anymore, clean up
                query = """
                        DELETE FROM active_tickets
                        WHERE server_id = $1 AND user_id = $2;
                        """
                await self.cog.bot.cxn.execute(query, interaction.guild.id, interaction.user.id)

        guild = interaction.guild
        mod_role = guild.get_role(config['ticket_role_id'])

        # Generate unique ticket ID
        ticket_id = random.randint(1000, 9999)
        thread_name = f"ticket-{ticket_id}"

        try:
            # Create a private thread directly from the channel
            thread = await interaction.channel.create_thread(
                name=thread_name,
                type=discord.ChannelType.private_thread,
                auto_archive_duration=4320,  # 3 days
                reason=f"Ticket created by {interaction.user}"
            )

            # Add the user to the thread
            await thread.add_user(interaction.user)

            # Add moderator role members to the thread if role exists
            if mod_role:
                for member in guild.members:
                    if mod_role in member.roles:
                        try:
                            await thread.add_user(member)
                        except:
                            pass  # Skip if can't add user

            # Store the active ticket in database
            query = """
                    INSERT INTO active_tickets (server_id, user_id, thread_id, ticket_id)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (server_id, user_id) DO UPDATE SET
                    thread_id = $3, ticket_id = $4;
                    """
            await self.cog.bot.cxn.execute(query, guild.id, interaction.user.id, thread.id, ticket_id)

            # Store in memory cache
            self.cog.active_tickets[interaction.user.id] = thread.id

            # Send initial message in thread
            embed = discord.Embed(
                title=f"🎫 Ticket opened!",
                description=f"Your ticket has been created at 📋 **{thread_name}**. A moderator will assist you shortly.",
                color=0x57F287,
                timestamp=discord.utils.utcnow()
            )

            await thread.send(f"{interaction.user.mention}")

            # Send the main ticket message
            ticket_embed = discord.Embed(
                title=f"This is your private ticket, {thread_name}!",
                description="Please provide any additional context or evidence if applicable.",
                color=0x5865F2
            )

            ticket_embed.add_field(
                name="📌 Notice",
                value="🔴 A Moderator will answer you as soon as they are able to do so. Please do not ping individual Moderators for assistance.",
                inline=False
            )

            ticket_embed.add_field(
                name="❌ Close Ticket",
                value="🔴 If this ticket was opened by mistake, you can close it below.",
                inline=False
            )

            # Add close button
            close_view = TicketCloseView(self.cog)
            await thread.send(embed=ticket_embed, view=close_view)

            # Notify moderators (without ping to avoid spam)
            if mod_role:
                mod_embed = discord.Embed(
                    description=f"📋 **{interaction.user}** has created a Support ticket.",
                    color=0xFFD700,
                    timestamp=discord.utils.utcnow()
                )
                mod_embed.add_field(
                    name="Entity/User ID",
                    value=f"`{interaction.user.id}`",
                    inline=False
                )
                await thread.send(embed=mod_embed)

            # Respond to the interaction
            await interaction.response.send_message(embed=embed, ephemeral=True)

        except discord.Forbidden:
            await interaction.response.send_message(
                "❌ I don't have permission to create threads in this channel.",
                ephemeral=True
            )
        except discord.HTTPException as e:
            await interaction.response.send_message(
                f"❌ Failed to create ticket: {str(e)}",
                ephemeral=True
            )

class TicketCloseView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.button(label="Close Ticket", style=discord.ButtonStyle.danger, custom_id="close_ticket")
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Load ticket configuration to check permissions
        config = await self.cog.load_ticket_config(interaction.guild.id)

        # Check if user has permission to close (ticket creator or moderator)
        is_ticket_creator = interaction.user.id in [member.id for member in interaction.channel.members]
        is_moderator = False

        if config['ticket_role_id']:
            mod_role = interaction.guild.get_role(config['ticket_role_id'])
            if mod_role:
                is_moderator = mod_role in interaction.user.roles

        if not (is_ticket_creator or is_moderator):
            return await interaction.response.send_message(
                "❌ You don't have permission to close this ticket.",
                ephemeral=True
            )

        # Archive and lock the thread
        await interaction.channel.edit(archived=True, locked=True)

        # Update channel name to show it's resolved
        try:
            new_name = f"[Resolved] {interaction.channel.name}"
            await interaction.channel.edit(name=new_name)
        except:
            pass

        # Remove from active tickets database
        query = """
                DELETE FROM active_tickets
                WHERE server_id = $1 AND thread_id = $2;
                """
        await self.cog.bot.cxn.execute(query, interaction.guild.id, interaction.channel.id)

        # Remove from memory cache
        user_id = None
        for uid, thread_id in self.cog.active_tickets.items():
            if thread_id == interaction.channel.id:
                user_id = uid
                break

        if user_id:
            del self.cog.active_tickets[user_id]

        embed = discord.Embed(
            description="🔒 This ticket has been locked and archived. Still need help? Create another ticket in the main channel.",
            color=0x57F287
        )
        await interaction.response.send_message(embed=embed)

    @discord.ui.button(label="Dismiss message", style=discord.ButtonStyle.secondary, custom_id="dismiss_message")
    async def dismiss_message(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_message("👁️ Only you can see this • Dismiss message", ephemeral=True)

    @commands.Cog.listener()
    async def on_ready(self):
        """Load active tickets when bot starts"""
        if not self.bot.cxn:
            return

        # Load active tickets for all guilds
        for guild in self.bot.guilds:
            try:
                await self.load_active_tickets(guild.id)
            except Exception as e:
                print(f"Failed to load active tickets for guild {guild.id}: {e}")

    @commands.Cog.listener()
    async def on_member_update(self, before, after):
        """Track when members lose their boost status"""
        if not self.bot.cxn:
            return

        # Check if member lost boost status
        if before.premium_since and not after.premium_since:
            try:
                query = """
                        INSERT INTO lost_boosters (server_id, user_id)
                        VALUES ($1, $2)
                        ON CONFLICT (server_id, user_id, lost_at) DO NOTHING;
                        """
                await self.bot.cxn.execute(query, after.guild.id, after.id)
            except Exception as e:
                print(f"Failed to track lost booster {after.id} in guild {after.guild.id}: {e}")

async def setup(bot):
    await bot.add_cog(Ticket(bot))

    # Add persistent views
    bot.add_view(TicketPanelView(bot.get_cog('Ticket')))
    bot.add_view(TicketCloseView(bot.get_cog('Ticket')))