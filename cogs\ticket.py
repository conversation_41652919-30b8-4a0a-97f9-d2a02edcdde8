import discord
from discord.ext import commands
import asyncio
from datetime import datetime
import random

TICKET_ROLE_ID = 123456789012345678  # Replace with your mod role ID
TICKET_CATEGORY_ID = 123456789012345678  # Replace with ticket category ID (optional)

class Ticket(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.panel_message_id = None
        self.active_tickets = {}  # Store active tickets {user_id: thread_id}

    @commands.command()
    @commands.has_permissions(administrator=True)
    async def ticketpanel(self, ctx):
        """Send the ticket panel."""
        embed = discord.Embed(
            title="🎫 Contact a Top.gg Moderator",
            description="Click the button below to **open a private ticket** with the @Moderators, official moderators of Top.gg.",
            color=0x5865F2
        )
        
        embed.add_field(
            name="Use this to:",
            value="• Report users, reviews, or entities\n• Request ownership transfer if you are unable to\n• Any other reason!",
            inline=False
        )
        
        embed.add_field(
            name="⚠️ Important",
            value="**This is not the place to discuss decline decisions. Please DM the Reviewer directly.**",
            inline=False
        )
        
        view = TicketPanelView(self)
        msg = await ctx.send(embed=embed, view=view)
        self.panel_message_id = msg.id

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def close(self, ctx):
        """Close a ticket thread."""
        if not isinstance(ctx.channel, discord.Thread):
            return await ctx.send("❌ This command can only be used in ticket threads.")
        
        if not ctx.channel.name.startswith("leo."):
            return await ctx.send("❌ This doesn't appear to be a ticket thread.")
        
        # Archive and lock the thread
        await ctx.channel.edit(archived=True, locked=True)
        
        # Update channel name to show it's resolved
        try:
            new_name = f"[Resolved] {ctx.channel.name}"
            await ctx.channel.edit(name=new_name)
        except:
            pass
        
        # Remove from active tickets
        user_id = None
        for uid, thread_id in self.active_tickets.items():
            if thread_id == ctx.channel.id:
                user_id = uid
                break
        
        if user_id:
            del self.active_tickets[user_id]
        
        embed = discord.Embed(
            description="🔒 This ticket has been locked and archived. Still need help? Create another ticket in the main channel.",
            color=0x57F287
        )
        await ctx.send(embed=embed)

class TicketPanelView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.button(label="Create Ticket", style=discord.ButtonStyle.blurple, custom_id="create_ticket")
    async def create_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Check if user already has an active ticket
        if interaction.user.id in self.cog.active_tickets:
            existing_thread_id = self.cog.active_tickets[interaction.user.id]
            try:
                existing_thread = interaction.guild.get_thread(existing_thread_id)
                if existing_thread and not existing_thread.archived:
                    return await interaction.response.send_message(
                        f"❌ You already have an active ticket: {existing_thread.mention}",
                        ephemeral=True
                    )
                else:
                    # Clean up old reference
                    del self.cog.active_tickets[interaction.user.id]
            except:
                # Thread doesn't exist anymore, clean up
                del self.cog.active_tickets[interaction.user.id]

        guild = interaction.guild
        mod_role = guild.get_role(TICKET_ROLE_ID)
        
        # Generate unique ticket ID
        ticket_id = random.randint(1000, 9999)
        thread_name = f"leo.{ticket_id}"

        try:
            # Create a private thread directly from the channel
            thread = await interaction.channel.create_thread(
                name=thread_name,
                type=discord.ChannelType.private_thread,
                auto_archive_duration=4320,  # 3 days
                reason=f"Ticket created by {interaction.user}"
            )
            
            # Add the user to the thread
            await thread.add_user(interaction.user)
            
            # Store the active ticket
            self.cog.active_tickets[interaction.user.id] = thread.id
            
            # Send initial message in thread
            embed = discord.Embed(
                title=f"🎫 Ticket opened!",
                description=f"Your ticket has been created at 📋 **{thread_name}**. A moderator will assist you shortly.",
                color=0x57F287,
                timestamp=datetime.utcnow()
            )
            
            await thread.send(f"{interaction.user.mention}")
            
            # Send the main ticket message
            ticket_embed = discord.Embed(
                title=f"This is your private ticket, {thread_name}!",
                description="Please provide any additional context or evidence if applicable.",
                color=0x5865F2
            )
            
            ticket_embed.add_field(
                name="📌 Notice",
                value="🔴 A Moderator will answer you as soon as they are able to do so. Please do not ping individual Moderators for assistance.",
                inline=False
            )
            
            ticket_embed.add_field(
                name="❌ Close Ticket",
                value="🔴 If this ticket was opened by mistake, you can close it below.",
                inline=False
            )
            
            # Add close button
            close_view = TicketCloseView(self.cog)
            await thread.send(embed=ticket_embed, view=close_view)
            
            # Notify moderators (without ping to avoid spam)
            if mod_role:
                mod_embed = discord.Embed(
                    description=f"📋 **{interaction.user}** has created a Moderator Support ticket.",
                    color=0xFFD700,
                    timestamp=datetime.utcnow()
                )
                mod_embed.add_field(
                    name="Entity/User ID",
                    value=f"`{interaction.user.id}`",
                    inline=False
                )
                await thread.send(embed=mod_embed)

            # Respond to the interaction
            await interaction.response.send_message(embed=embed, ephemeral=True)
            
        except discord.Forbidden:
            await interaction.response.send_message(
                "❌ I don't have permission to create threads in this channel.",
                ephemeral=True
            )
        except discord.HTTPException as e:
            await interaction.response.send_message(
                f"❌ Failed to create ticket: {str(e)}",
                ephemeral=True
            )

class TicketCloseView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.button(label="Close Ticket", style=discord.ButtonStyle.danger, custom_id="close_ticket")
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Only allow the ticket creator or moderators to close
        if (interaction.user.id not in [member.id for member in interaction.channel.members] or 
            not any(role.id == TICKET_ROLE_ID for role in interaction.user.roles)):
            return await interaction.response.send_message(
                "❌ You don't have permission to close this ticket.",
                ephemeral=True
            )

        # Archive and lock the thread
        await interaction.channel.edit(archived=True, locked=True)
        
        # Update channel name to show it's resolved
        try:
            new_name = f"[Resolved] {interaction.channel.name}"
            await interaction.channel.edit(name=new_name)
        except:
            pass
        
        # Remove from active tickets
        user_id = None
        for uid, thread_id in self.cog.active_tickets.items():
            if thread_id == interaction.channel.id:
                user_id = uid
                break
        
        if user_id:
            del self.cog.active_tickets[user_id]
        
        embed = discord.Embed(
            description="🔒 This ticket has been locked and archived. Still need help? Create another ticket in # mod-tickets",
            color=0x57F287
        )
        await interaction.response.send_message(embed=embed)

    @discord.ui.button(label="Dismiss message", style=discord.ButtonStyle.secondary, custom_id="dismiss_message")
    async def dismiss_message(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_message("👁️ Only you can see this • Dismiss message", ephemeral=True)

async def setup(bot):
    await bot.add_cog(Ticket(bot))
    
    # Add persistent views
    bot.add_view(TicketPanelView(bot.get_cog('Ticket')))
    bot.add_view(TicketCloseView(bot.get_cog('Ticket')))