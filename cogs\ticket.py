import discord
from discord.ext import commands
import asyncio
import random

from utilities import checks
from utilities import decorators

class Ticket(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.panel_message_id = None
        self.active_tickets = {}  # Store active tickets {user_id: thread_id}
        self.ticket_configs = {}  # Cache for ticket configurations

    async def ensure_tables_exist(self):
        """Ensure ticket tables exist in the database"""
        if not self.bot.cxn:
            return False

        try:
            # Force execute the SQL scripts to ensure tables exist
            await self.bot.database.scriptexec()
            return True
        except Exception as e:
            print(f"Failed to execute database scripts: {e}")
            return False

    async def load_ticket_config(self, guild_id):
        """Load ticket configuration from database"""
        # Ensure tables exist first
        await self.ensure_tables_exist()

        if guild_id in self.ticket_configs:
            return self.ticket_configs[guild_id]

        try:
            query = """
                    SELECT * FROM ticket_config
                    WHERE server_id = $1;
                    """
            config = await self.bot.cxn.fetchrow(query, guild_id)
        except Exception as e:
            print(f"Error loading ticket config: {e}")
            # Force table creation and try again
            await self.ensure_tables_exist()
            try:
                config = await self.bot.cxn.fetchrow(query, guild_id)
            except Exception as e2:
                print(f"Failed to load config after table creation: {e2}")
                return None

        if not config:
            # Create default config
            default_config = {
                'server_id': guild_id,
                'ticket_role_id': None,
                'ticket_category_id': None,
                'panel_title': '🎫 Contact a Moderator',
                'panel_description': 'Click the button below to **open a private ticket** with the moderators.',
                'panel_color': '#5865F2',
                'button_style': 'blurple',
                'button_emoji': '🎫',
                'use_dropdown': False
            }

            query = """
                    INSERT INTO ticket_config (server_id, ticket_role_id, ticket_category_id, panel_title, panel_description, panel_color, button_style, button_emoji, use_dropdown)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    ON CONFLICT (server_id) DO NOTHING;
                    """
            await self.bot.cxn.execute(query, guild_id, None, None, default_config['panel_title'],
                                     default_config['panel_description'], default_config['panel_color'],
                                     default_config['button_style'], default_config['button_emoji'], default_config['use_dropdown'])

            self.ticket_configs[guild_id] = default_config
            return default_config

        config_dict = dict(config)
        self.ticket_configs[guild_id] = config_dict
        return config_dict

    async def load_active_tickets(self, guild_id):
        """Load active tickets from database"""
        if not await self.ensure_tables_exist():
            return

        query = """
                SELECT user_id, thread_id FROM active_tickets
                WHERE server_id = $1;
                """
        records = await self.bot.cxn.fetch(query, guild_id)

        for record in records:
            self.active_tickets[record['user_id']] = record['thread_id']

    @decorators.command(
        brief="Configure ticket system with interactive setup.",
        aliases=["ticketconfig", "tconfig"],
        examples="""
                {0}ticketsetup
                """,
    )
    @checks.has_perms(administrator=True)
    @checks.bot_has_perms(embed_links=True)
    async def ticketsetup(self, ctx):
        """
        Usage: {0}ticketsetup
        Aliases: {0}ticketconfig, {0}tconfig
        Output: Interactive ticket system configuration with buttons
        """
        config = await self.load_ticket_config(ctx.guild.id)
        if not config:
            return await ctx.fail("Failed to load ticket configuration. Please try again.")

        embed = discord.Embed(
            title="🎫 Ticket System Configuration",
            description="Use the buttons below to configure your ticket system.",
            color=self.bot.mode.EMBED_COLOR
        )

        role = ctx.guild.get_role(config['ticket_role_id']) if config['ticket_role_id'] else None
        category = ctx.guild.get_channel(config['ticket_category_id']) if config['ticket_category_id'] else None

        embed.add_field(
            name="📋 Current Settings",
            value=f"**Moderator Role:** {role.mention if role else '`Not set`'}\n"
                  f"**Category:** {category.name if category else '`Not set`'}\n"
                  f"**Panel Title:** {config['panel_title']}\n"
                  f"**Panel Color:** {config['panel_color']}\n"
                  f"**Button Style:** {config.get('button_style', 'blurple').title()}\n"
                  f"**Button Emoji:** {config.get('button_emoji', '🎫')}\n"
                  f"**Use Dropdown:** {'Yes' if config.get('use_dropdown', False) else 'No'}",
            inline=False
        )

        if not role:
            embed.add_field(
                name="⚠️ Setup Required",
                value="You must set a moderator role before the ticket system can be used.",
                inline=False
            )

        view = TicketSetupView(self, ctx.guild.id)
        await ctx.send_or_reply(embed=embed, view=view)

class TicketSetupView(discord.ui.View):
    def __init__(self, cog, guild_id):
        super().__init__(timeout=300)
        self.cog = cog
        self.guild_id = guild_id

    @discord.ui.button(label="Set Role", emoji="👥", style=discord.ButtonStyle.primary)
    async def set_role(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_modal(RoleModal(self.cog, self.guild_id))

    @discord.ui.button(label="Set Category", emoji="📁", style=discord.ButtonStyle.primary)
    async def set_category(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_modal(CategoryModal(self.cog, self.guild_id))

    @discord.ui.button(label="Panel Settings", emoji="🎨", style=discord.ButtonStyle.secondary)
    async def panel_settings(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_modal(PanelModal(self.cog, self.guild_id))

    @discord.ui.button(label="Button Style", emoji="🔘", style=discord.ButtonStyle.secondary)
    async def button_style(self, interaction: discord.Interaction, button: discord.ui.Button):
        view = ButtonStyleView(self.cog, self.guild_id)
        await interaction.response.send_message("Choose button style:", view=view, ephemeral=True)

    @discord.ui.button(label="Send Panel", emoji="📤", style=discord.ButtonStyle.success)
    async def send_panel(self, interaction: discord.Interaction, button: discord.ui.Button):
        config = await self.cog.load_ticket_config(self.guild_id)

        if not config['ticket_role_id']:
            return await interaction.response.send_message(
                "❌ Please set a moderator role first!", ephemeral=True
            )

        try:
            color = int(config['panel_color'][1:], 16)
        except:
            color = 0x5865F2

        embed = discord.Embed(
            title=config['panel_title'],
            description=config['panel_description'],
            color=color
        )

        if config.get('use_dropdown', False):
            view = TicketDropdownView(self.cog)
        else:
            view = TicketPanelView(self.cog)

        await interaction.response.send_message(embed=embed, view=view)

    @discord.ui.button(label="Refresh", emoji="🔄", style=discord.ButtonStyle.gray)
    async def refresh(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Refresh the configuration display
        config = await self.cog.load_ticket_config(self.guild_id)
        guild = interaction.guild

        embed = discord.Embed(
            title="🎫 Ticket System Configuration",
            description="Use the buttons below to configure your ticket system.",
            color=self.cog.bot.mode.EMBED_COLOR
        )

        role = guild.get_role(config['ticket_role_id']) if config['ticket_role_id'] else None
        category = guild.get_channel(config['ticket_category_id']) if config['ticket_category_id'] else None

        embed.add_field(
            name="📋 Current Settings",
            value=f"**Moderator Role:** {role.mention if role else '`Not set`'}\n"
                  f"**Category:** {category.name if category else '`Not set`'}\n"
                  f"**Panel Title:** {config['panel_title']}\n"
                  f"**Panel Color:** {config['panel_color']}\n"
                  f"**Button Style:** {config.get('button_style', 'blurple').title()}\n"
                  f"**Button Emoji:** {config.get('button_emoji', '🎫')}\n"
                  f"**Use Dropdown:** {'Yes' if config.get('use_dropdown', False) else 'No'}",
            inline=False
        )

        if not role:
            embed.add_field(
                name="⚠️ Setup Required",
                value="You must set a moderator role before the ticket system can be used.",
                inline=False
            )

        await interaction.response.edit_message(embed=embed, view=self)

class RoleModal(discord.ui.Modal, title="Set Moderator Role"):
    def __init__(self, cog, guild_id):
        super().__init__()
        self.cog = cog
        self.guild_id = guild_id

    role_input = discord.ui.TextInput(
        label="Role Name or ID",
        placeholder="Enter role name or ID (e.g., @Moderators or 123456789)",
        required=True,
        max_length=100
    )

    async def on_submit(self, interaction: discord.Interaction):
        try:
            # Try to convert role
            role = None
            role_input = self.role_input.value.strip()

            # Remove @ if present
            if role_input.startswith('@'):
                role_input = role_input[1:]

            # Try by ID first
            if role_input.isdigit():
                role = interaction.guild.get_role(int(role_input))

            # Try by name if ID didn't work
            if not role:
                role = discord.utils.get(interaction.guild.roles, name=role_input)

            if not role:
                return await interaction.response.send_message(
                    f"❌ Could not find role: `{self.role_input.value}`", ephemeral=True
                )

            # Update database
            query = """
                    UPDATE ticket_config
                    SET ticket_role_id = $1, updated_at = NOW()
                    WHERE server_id = $2;
                    """
            await self.cog.bot.cxn.execute(query, role.id, self.guild_id)

            # Update cache
            if self.guild_id in self.cog.ticket_configs:
                self.cog.ticket_configs[self.guild_id]['ticket_role_id'] = role.id

            await interaction.response.send_message(
                f"✅ Moderator role set to {role.mention}", ephemeral=True
            )

        except Exception as e:
            await interaction.response.send_message(
                f"❌ Error setting role: {str(e)}", ephemeral=True
            )

class CategoryModal(discord.ui.Modal, title="Set Ticket Category"):
    def __init__(self, cog, guild_id):
        super().__init__()
        self.cog = cog
        self.guild_id = guild_id

    category_input = discord.ui.TextInput(
        label="Category Name or ID",
        placeholder="Enter category name or ID (optional)",
        required=False,
        max_length=100
    )

    async def on_submit(self, interaction: discord.Interaction):
        try:
            category = None
            if self.category_input.value.strip():
                category_input = self.category_input.value.strip()

                # Try by ID first
                if category_input.isdigit():
                    category = interaction.guild.get_channel(int(category_input))

                # Try by name if ID didn't work
                if not category:
                    category = discord.utils.get(interaction.guild.categories, name=category_input)

                if not category:
                    return await interaction.response.send_message(
                        f"❌ Could not find category: `{self.category_input.value}`", ephemeral=True
                    )

                if not isinstance(category, discord.CategoryChannel):
                    return await interaction.response.send_message(
                        f"❌ `{category.name}` is not a category channel!", ephemeral=True
                    )

            # Update database
            query = """
                    UPDATE ticket_config
                    SET ticket_category_id = $1, updated_at = NOW()
                    WHERE server_id = $2;
                    """
            await self.cog.bot.cxn.execute(query, category.id if category else None, self.guild_id)

            # Update cache
            if self.guild_id in self.cog.ticket_configs:
                self.cog.ticket_configs[self.guild_id]['ticket_category_id'] = category.id if category else None

            if category:
                await interaction.response.send_message(
                    f"✅ Ticket category set to `{category.name}`", ephemeral=True
                )
            else:
                await interaction.response.send_message(
                    "✅ Ticket category cleared (will use current channel)", ephemeral=True
                )

        except Exception as e:
            await interaction.response.send_message(
                f"❌ Error setting category: {str(e)}", ephemeral=True
            )

class PanelModal(discord.ui.Modal, title="Panel Settings"):
    def __init__(self, cog, guild_id):
        super().__init__()
        self.cog = cog
        self.guild_id = guild_id

    title_input = discord.ui.TextInput(
        label="Panel Title",
        placeholder="🎫 Contact a Moderator",
        required=True,
        max_length=256
    )

    description_input = discord.ui.TextInput(
        label="Panel Description",
        placeholder="Click the button below to open a private ticket...",
        style=discord.TextStyle.paragraph,
        required=True,
        max_length=4000
    )

    color_input = discord.ui.TextInput(
        label="Panel Color (Hex)",
        placeholder="#5865F2",
        required=False,
        max_length=7
    )

    emoji_input = discord.ui.TextInput(
        label="Button Emoji",
        placeholder="🎫",
        required=False,
        max_length=10
    )

    async def on_submit(self, interaction: discord.Interaction):
        try:
            # Validate color
            color = self.color_input.value.strip() or "#5865F2"
            if not color.startswith('#'):
                color = '#' + color

            try:
                int(color[1:], 16)
                if len(color) != 7:
                    raise ValueError
            except ValueError:
                return await interaction.response.send_message(
                    "❌ Invalid hex color format. Use format: #FF5733", ephemeral=True
                )

            # Update database
            query = """
                    UPDATE ticket_config
                    SET panel_title = $1, panel_description = $2, panel_color = $3, button_emoji = $4, updated_at = NOW()
                    WHERE server_id = $5;
                    """
            await self.cog.bot.cxn.execute(
                query,
                self.title_input.value.strip(),
                self.description_input.value.strip(),
                color,
                self.emoji_input.value.strip() or "🎫",
                self.guild_id
            )

            # Update cache
            if self.guild_id in self.cog.ticket_configs:
                self.cog.ticket_configs[self.guild_id].update({
                    'panel_title': self.title_input.value.strip(),
                    'panel_description': self.description_input.value.strip(),
                    'panel_color': color,
                    'button_emoji': self.emoji_input.value.strip() or "🎫"
                })

            await interaction.response.send_message(
                "✅ Panel settings updated successfully!", ephemeral=True
            )

        except Exception as e:
            await interaction.response.send_message(
                f"❌ Error updating panel settings: {str(e)}", ephemeral=True
            )

class ButtonStyleView(discord.ui.View):
    def __init__(self, cog, guild_id):
        super().__init__(timeout=60)
        self.cog = cog
        self.guild_id = guild_id

    @discord.ui.button(label="Primary", style=discord.ButtonStyle.primary)
    async def primary_style(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.update_style(interaction, "primary")

    @discord.ui.button(label="Secondary", style=discord.ButtonStyle.secondary)
    async def secondary_style(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.update_style(interaction, "secondary")

    @discord.ui.button(label="Success", style=discord.ButtonStyle.success)
    async def success_style(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.update_style(interaction, "success")

    @discord.ui.button(label="Danger", style=discord.ButtonStyle.danger)
    async def danger_style(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.update_style(interaction, "danger")

    @discord.ui.button(label="Use Dropdown", style=discord.ButtonStyle.gray, emoji="📋")
    async def dropdown_toggle(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            # Toggle dropdown setting
            config = await self.cog.load_ticket_config(self.guild_id)
            new_dropdown = not config.get('use_dropdown', False)

            query = """
                    UPDATE ticket_config
                    SET use_dropdown = $1, updated_at = NOW()
                    WHERE server_id = $2;
                    """
            await self.cog.bot.cxn.execute(query, new_dropdown, self.guild_id)

            # Update cache
            if self.guild_id in self.cog.ticket_configs:
                self.cog.ticket_configs[self.guild_id]['use_dropdown'] = new_dropdown

            await interaction.response.send_message(
                f"✅ {'Enabled' if new_dropdown else 'Disabled'} dropdown menu for tickets!", ephemeral=True
            )

        except Exception as e:
            await interaction.response.send_message(
                f"❌ Error toggling dropdown: {str(e)}", ephemeral=True
            )

    async def update_style(self, interaction, style):
        try:
            query = """
                    UPDATE ticket_config
                    SET button_style = $1, updated_at = NOW()
                    WHERE server_id = $2;
                    """
            await self.cog.bot.cxn.execute(query, style, self.guild_id)

            # Update cache
            if self.guild_id in self.cog.ticket_configs:
                self.cog.ticket_configs[self.guild_id]['button_style'] = style

            await interaction.response.send_message(
                f"✅ Button style set to {style.title()}!", ephemeral=True
            )

        except Exception as e:
            await interaction.response.send_message(
                f"❌ Error setting button style: {str(e)}", ephemeral=True
            )

class TicketDropdownView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.select(
        placeholder="Select a ticket type...",
        options=[
            discord.SelectOption(
                label="General Support",
                description="General questions and support",
                emoji="❓",
                value="general"
            ),
            discord.SelectOption(
                label="Bug Report",
                description="Report a bug or issue",
                emoji="🐛",
                value="bug"
            ),
            discord.SelectOption(
                label="Feature Request",
                description="Request a new feature",
                emoji="💡",
                value="feature"
            ),
            discord.SelectOption(
                label="Other",
                description="Other inquiries",
                emoji="📝",
                value="other"
            )
        ]
    )
    async def ticket_dropdown(self, interaction: discord.Interaction, select: discord.ui.Select):
        # Create ticket with selected type
        await self.create_ticket_with_type(interaction, select.values[0])

    async def create_ticket_with_type(self, interaction, ticket_type):
        # Load ticket configuration
        config = await self.cog.load_ticket_config(interaction.guild.id)

        if not config['ticket_role_id']:
            return await interaction.response.send_message(
                "❌ Ticket system is not properly configured. Please contact an administrator.",
                ephemeral=True
            )

        # Check if user already has an active ticket
        query = """
                SELECT thread_id FROM active_tickets
                WHERE server_id = $1 AND user_id = $2;
                """
        existing_ticket = await self.cog.bot.cxn.fetchrow(query, interaction.guild.id, interaction.user.id)

        if existing_ticket:
            try:
                existing_thread = interaction.guild.get_thread(existing_ticket['thread_id'])
                if existing_thread and not existing_thread.archived:
                    return await interaction.response.send_message(
                        f"❌ You already have an active ticket: {existing_thread.mention}",
                        ephemeral=True
                    )
                else:
                    # Clean up old reference from database
                    query = """
                            DELETE FROM active_tickets
                            WHERE server_id = $1 AND user_id = $2;
                            """
                    await self.cog.bot.cxn.execute(query, interaction.guild.id, interaction.user.id)
            except:
                # Thread doesn't exist anymore, clean up
                query = """
                        DELETE FROM active_tickets
                        WHERE server_id = $1 AND user_id = $2;
                        """
                await self.cog.bot.cxn.execute(query, interaction.guild.id, interaction.user.id)

        guild = interaction.guild
        mod_role = guild.get_role(config['ticket_role_id'])

        # Generate unique ticket ID
        ticket_id = random.randint(1000, 9999)
        thread_name = f"ticket-{ticket_type}-{ticket_id}"

        try:
            # Create a private thread directly from the channel
            thread = await interaction.channel.create_thread(
                name=thread_name,
                type=discord.ChannelType.private_thread,
                auto_archive_duration=4320,  # 3 days
                reason=f"Ticket created by {interaction.user} - Type: {ticket_type}"
            )

            # Add the user to the thread
            await thread.add_user(interaction.user)

            # Add moderator role members to the thread if role exists
            if mod_role:
                # Add all online moderators first
                online_mods = [member for member in guild.members if mod_role in member.roles and member.status != discord.Status.offline]
                for member in online_mods[:10]:  # Limit to 10 online mods to avoid rate limits
                    try:
                        await thread.add_user(member)
                    except Exception as e:
                        print(f"Failed to add moderator {member} to ticket: {e}")

                # If no online mods, add some offline ones
                if not online_mods:
                    offline_mods = [member for member in guild.members if mod_role in member.roles][:5]
                    for member in offline_mods:
                        try:
                            await thread.add_user(member)
                        except Exception as e:
                            print(f"Failed to add offline moderator {member} to ticket: {e}")

            # Store the active ticket in database
            query = """
                    INSERT INTO active_tickets (server_id, user_id, thread_id, ticket_id)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (server_id, user_id) DO UPDATE SET
                    thread_id = $3, ticket_id = $4;
                    """
            await self.cog.bot.cxn.execute(query, guild.id, interaction.user.id, thread.id, ticket_id)

            # Store in memory cache
            self.cog.active_tickets[interaction.user.id] = thread.id

            # Send initial message in thread
            embed = discord.Embed(
                title=f"🎫 {ticket_type.title()} Ticket opened!",
                description=f"Your ticket has been created at 📋 **{thread_name}**. A moderator will assist you shortly.",
                color=0x57F287,
                timestamp=discord.utils.utcnow()
            )

            await thread.send(f"{interaction.user.mention}")

            # Send the main ticket message
            ticket_embed = discord.Embed(
                title=f"This is your private {ticket_type} ticket, {thread_name}!",
                description="Please provide any additional context or evidence if applicable.",
                color=0x5865F2
            )

            ticket_embed.add_field(
                name="📌 Notice",
                value="🔴 A Moderator will answer you as soon as they are able to do so. Please do not ping individual Moderators for assistance.",
                inline=False
            )

            ticket_embed.add_field(
                name="❌ Close Ticket",
                value="🔴 If this ticket was opened by mistake, you can close it below.",
                inline=False
            )

            # Add close button
            close_view = TicketCloseView(self.cog)
            await thread.send(embed=ticket_embed, view=close_view)

            # Notify moderators (without ping to avoid spam)
            if mod_role:
                mod_embed = discord.Embed(
                    description=f"📋 **{interaction.user}** has created a {ticket_type.title()} ticket.",
                    color=0xFFD700,
                    timestamp=discord.utils.utcnow()
                )
                mod_embed.add_field(
                    name="Entity/User ID",
                    value=f"`{interaction.user.id}`",
                    inline=False
                )
                await thread.send(embed=mod_embed)

            # Respond to the interaction
            await interaction.response.send_message(embed=embed, ephemeral=True)

        except discord.Forbidden:
            await interaction.response.send_message(
                "❌ I don't have permission to create threads in this channel.",
                ephemeral=True
            )
        except discord.HTTPException as e:
            await interaction.response.send_message(
                f"❌ Failed to create ticket: {str(e)}",
                ephemeral=True
            )

    @decorators.command(
        brief="Send the ticket panel.",
        aliases=["tpanel"],
        examples="""
                {0}ticketpanel
                """,
    )
    @checks.has_perms(administrator=True)
    @checks.bot_has_perms(embed_links=True)
    async def ticketpanel(self, ctx):
        """
        Usage: {0}ticketpanel
        Alias: {0}tpanel
        Output: Send the ticket panel with current configuration
        """
        config = await self.load_ticket_config(ctx.guild.id)

        if not config or not config['ticket_role_id']:
            return await ctx.fail("Please configure the ticket system first using `ticketsetup`")

        try:
            color = int(config['panel_color'][1:], 16)
        except:
            color = 0x5865F2

        embed = discord.Embed(
            title=config['panel_title'],
            description=config['panel_description'],
            color=color
        )

        if config.get('use_dropdown', False):
            view = TicketDropdownView(self)
        else:
            view = TicketPanelView(self)

        msg = await ctx.send(embed=embed, view=view)
        self.panel_message_id = msg.id

    @decorators.command(
        brief="Close a ticket thread.",
        aliases=["closeticket"],
        examples="""
                {0}close
                """,
    )
    @checks.has_perms(manage_messages=True)
    async def close(self, ctx):
        """
        Usage: {0}close
        Alias: {0}closeticket
        Output: Close the current ticket thread
        """
        if not isinstance(ctx.channel, discord.Thread):
            return await ctx.send("❌ This command can only be used in ticket threads.")

        if not ctx.channel.name.startswith("ticket-"):
            return await ctx.send("❌ This doesn't appear to be a ticket thread.")

        # Archive and lock the thread
        await ctx.channel.edit(archived=True, locked=True)

        # Update channel name to show it's resolved
        try:
            new_name = f"[Resolved] {ctx.channel.name}"
            await ctx.channel.edit(name=new_name)
        except:
            pass

        # Remove from active tickets database
        query = """
                DELETE FROM active_tickets
                WHERE server_id = $1 AND thread_id = $2;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, ctx.channel.id)

        # Remove from memory cache
        user_id = None
        for uid, thread_id in self.active_tickets.items():
            if thread_id == ctx.channel.id:
                user_id = uid
                break

        if user_id:
            del self.active_tickets[user_id]

        embed = discord.Embed(
            description="🔒 This ticket has been locked and archived. Still need help? Create another ticket in the main channel.",
            color=0x57F287
        )
        await ctx.send(embed=embed)

class TicketPanelView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.button(label="Create Ticket", style=discord.ButtonStyle.blurple, custom_id="create_ticket")
    async def create_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Load ticket configuration
        config = await self.cog.load_ticket_config(interaction.guild.id)

        if not config['ticket_role_id']:
            return await interaction.response.send_message(
                "❌ Ticket system is not properly configured. Please contact an administrator.",
                ephemeral=True
            )

        # Check if user already has an active ticket
        query = """
                SELECT thread_id FROM active_tickets
                WHERE server_id = $1 AND user_id = $2;
                """
        existing_ticket = await self.cog.bot.cxn.fetchrow(query, interaction.guild.id, interaction.user.id)

        if existing_ticket:
            try:
                existing_thread = interaction.guild.get_thread(existing_ticket['thread_id'])
                if existing_thread and not existing_thread.archived:
                    return await interaction.response.send_message(
                        f"❌ You already have an active ticket: {existing_thread.mention}",
                        ephemeral=True
                    )
                else:
                    # Clean up old reference from database
                    query = """
                            DELETE FROM active_tickets
                            WHERE server_id = $1 AND user_id = $2;
                            """
                    await self.cog.bot.cxn.execute(query, interaction.guild.id, interaction.user.id)
            except:
                # Thread doesn't exist anymore, clean up
                query = """
                        DELETE FROM active_tickets
                        WHERE server_id = $1 AND user_id = $2;
                        """
                await self.cog.bot.cxn.execute(query, interaction.guild.id, interaction.user.id)

        guild = interaction.guild
        mod_role = guild.get_role(config['ticket_role_id'])

        # Generate unique ticket ID
        ticket_id = random.randint(1000, 9999)
        thread_name = f"ticket-{ticket_id}"

        try:
            # Create a private thread directly from the channel
            thread = await interaction.channel.create_thread(
                name=thread_name,
                type=discord.ChannelType.private_thread,
                auto_archive_duration=4320,  # 3 days
                reason=f"Ticket created by {interaction.user}"
            )

            # Add the user to the thread
            await thread.add_user(interaction.user)

            # Add moderator role members to the thread if role exists
            if mod_role:
                # Add all online moderators first
                online_mods = [member for member in guild.members if mod_role in member.roles and member.status != discord.Status.offline]
                for member in online_mods[:10]:  # Limit to 10 online mods to avoid rate limits
                    try:
                        await thread.add_user(member)
                    except Exception as e:
                        print(f"Failed to add moderator {member} to ticket: {e}")

                # If no online mods, add some offline ones
                if not online_mods:
                    offline_mods = [member for member in guild.members if mod_role in member.roles][:5]
                    for member in offline_mods:
                        try:
                            await thread.add_user(member)
                        except Exception as e:
                            print(f"Failed to add offline moderator {member} to ticket: {e}")

            # Store the active ticket in database
            query = """
                    INSERT INTO active_tickets (server_id, user_id, thread_id, ticket_id)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (server_id, user_id) DO UPDATE SET
                    thread_id = $3, ticket_id = $4;
                    """
            await self.cog.bot.cxn.execute(query, guild.id, interaction.user.id, thread.id, ticket_id)

            # Store in memory cache
            self.cog.active_tickets[interaction.user.id] = thread.id

            # Send initial message in thread
            embed = discord.Embed(
                title=f"🎫 Ticket opened!",
                description=f"Your ticket has been created at 📋 **{thread_name}**. A moderator will assist you shortly.",
                color=0x57F287,
                timestamp=discord.utils.utcnow()
            )

            await thread.send(f"{interaction.user.mention}")

            # Send the main ticket message
            ticket_embed = discord.Embed(
                title=f"This is your private ticket, {thread_name}!",
                description="Please provide any additional context or evidence if applicable.",
                color=0x5865F2
            )

            ticket_embed.add_field(
                name="📌 Notice",
                value="🔴 A Moderator will answer you as soon as they are able to do so. Please do not ping individual Moderators for assistance.",
                inline=False
            )

            ticket_embed.add_field(
                name="❌ Close Ticket",
                value="🔴 If this ticket was opened by mistake, you can close it below.",
                inline=False
            )

            # Add close button
            close_view = TicketCloseView(self.cog)
            await thread.send(embed=ticket_embed, view=close_view)

            # Notify moderators (without ping to avoid spam)
            if mod_role:
                mod_embed = discord.Embed(
                    description=f"📋 **{interaction.user}** has created a Support ticket.",
                    color=0xFFD700,
                    timestamp=discord.utils.utcnow()
                )
                mod_embed.add_field(
                    name="Entity/User ID",
                    value=f"`{interaction.user.id}`",
                    inline=False
                )
                await thread.send(embed=mod_embed)

            # Respond to the interaction
            await interaction.response.send_message(embed=embed, ephemeral=True)

        except discord.Forbidden:
            await interaction.response.send_message(
                "❌ I don't have permission to create threads in this channel.",
                ephemeral=True
            )
        except discord.HTTPException as e:
            await interaction.response.send_message(
                f"❌ Failed to create ticket: {str(e)}",
                ephemeral=True
            )

class TicketCloseView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.button(label="Close Ticket", style=discord.ButtonStyle.danger, custom_id="close_ticket")
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Load ticket configuration to check permissions
        config = await self.cog.load_ticket_config(interaction.guild.id)

        # Check if user has permission to close (ticket creator or moderator)
        is_ticket_creator = interaction.user.id in [member.id for member in interaction.channel.members]
        is_moderator = False

        if config['ticket_role_id']:
            mod_role = interaction.guild.get_role(config['ticket_role_id'])
            if mod_role:
                is_moderator = mod_role in interaction.user.roles

        if not (is_ticket_creator or is_moderator):
            return await interaction.response.send_message(
                "❌ You don't have permission to close this ticket.",
                ephemeral=True
            )

        # Archive and lock the thread
        await interaction.channel.edit(archived=True, locked=True)

        # Update channel name to show it's resolved
        try:
            new_name = f"[Resolved] {interaction.channel.name}"
            await interaction.channel.edit(name=new_name)
        except:
            pass

        # Remove from active tickets database
        query = """
                DELETE FROM active_tickets
                WHERE server_id = $1 AND thread_id = $2;
                """
        await self.cog.bot.cxn.execute(query, interaction.guild.id, interaction.channel.id)

        # Remove from memory cache
        user_id = None
        for uid, thread_id in self.cog.active_tickets.items():
            if thread_id == interaction.channel.id:
                user_id = uid
                break

        if user_id:
            del self.cog.active_tickets[user_id]

        embed = discord.Embed(
            description="🔒 This ticket has been locked and archived. Still need help? Create another ticket in the main channel.",
            color=0x57F287
        )
        await interaction.response.send_message(embed=embed)

    @discord.ui.button(label="Dismiss message", style=discord.ButtonStyle.secondary, custom_id="dismiss_message")
    async def dismiss_message(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_message("👁️ Only you can see this • Dismiss message", ephemeral=True)

    @commands.Cog.listener()
    async def on_ready(self):
        """Load active tickets when bot starts"""
        if not self.bot.cxn:
            return

        # Ensure tables exist first
        await self.ensure_tables_exist()

        # Load active tickets for all guilds
        for guild in self.bot.guilds:
            try:
                await self.load_active_tickets(guild.id)
            except Exception as e:
                print(f"Failed to load active tickets for guild {guild.id}: {e}")

    async def cog_load(self):
        """Called when the cog is loaded"""
        if self.bot.cxn:
            await self.ensure_tables_exist()

    async def cog_load(self):
        """Called when the cog is loaded"""
        if self.bot.cxn:
            await self.ensure_tables_exist()

    @commands.Cog.listener()
    async def on_member_update(self, before, after):
        """Track when members lose their boost status"""
        if not self.bot.cxn:
            return

        # Check if member lost boost status
        if before.premium_since and not after.premium_since:
            try:
                query = """
                        INSERT INTO lost_boosters (server_id, user_id)
                        VALUES ($1, $2)
                        ON CONFLICT (server_id, user_id, lost_at) DO NOTHING;
                        """
                await self.bot.cxn.execute(query, after.guild.id, after.id)
            except Exception as e:
                print(f"Failed to track lost booster {after.id} in guild {after.guild.id}: {e}")

async def setup(bot):
    await bot.add_cog(Ticket(bot))

    # Add persistent views
    bot.add_view(TicketPanelView(bot.get_cog('Ticket')))
    bot.add_view(TicketCloseView(bot.get_cog('Ticket')))